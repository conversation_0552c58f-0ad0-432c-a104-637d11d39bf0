#!/usr/bin/env python3
"""
测试OpenAI客户端修复
验证新版本的OpenAI库是否能正常初始化
"""

def test_openai_client():
    """测试OpenAI客户端初始化"""
    print("=== OpenAI客户端修复测试 ===")
    
    try:
        from openai import OpenAI
        print("✅ 成功导入OpenAI库")
        
        # 测试客户端初始化
        client = OpenAI(
            api_key="test-key",
            base_url="https://api.openai.com/v1"
        )
        print("✅ OpenAI客户端初始化成功")
        print(f"   客户端类型: {type(client)}")
        print(f"   API Key: {client.api_key[:10]}...")
        print(f"   Base URL: {client.base_url}")
        
        # 测试客户端属性
        print(f"   默认超时: {client.timeout}")
        print(f"   最大重试: {client.max_retries}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_agent_service_import():
    """测试AgentService导入（不连接外部服务）"""
    print("\n=== AgentService导入测试 ===")
    
    try:
        # 设置测试环境变量
        import os
        os.environ['LLM_API_KEY'] = 'test-key'
        os.environ['LLM_BASE_URL'] = 'https://api.openai.com/v1'
        os.environ['LLM_MODEL'] = 'gpt-3.5-turbo'
        
        # 模拟配置
        class MockSettings:
            LLM_API_KEY = 'test-key'
            OPENAI_API_KEY = 'test-key'
            LLM_BASE_URL = 'https://api.openai.com/v1'
            OPENAI_BASE_URL = 'https://api.openai.com/v1'
            LLM_MODEL = 'gpt-3.5-turbo'
            OPENAI_MODEL = 'gpt-3.5-turbo'
        
        # 测试OpenAI客户端初始化逻辑
        from openai import OpenAI
        
        settings = MockSettings()
        llm_client = OpenAI(
            api_key=settings.LLM_API_KEY or settings.OPENAI_API_KEY,
            base_url=settings.LLM_BASE_URL or settings.OPENAI_BASE_URL
        )
        model = settings.LLM_MODEL or settings.OPENAI_MODEL
        
        print("✅ AgentService初始化逻辑测试成功")
        print(f"   使用模型: {model}")
        print(f"   客户端类型: {type(llm_client)}")
        
        return True
        
    except Exception as e:
        print(f"❌ AgentService测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_openai_version():
    """检查OpenAI版本"""
    print("\n=== OpenAI版本检查 ===")
    
    try:
        import openai
        print(f"✅ OpenAI版本: {openai.__version__}")
        
        # 检查是否支持新的初始化方式
        from openai import OpenAI
        print("✅ 支持新的OpenAI客户端类")
        
        return True
        
    except Exception as e:
        print(f"❌ 版本检查失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试OpenAI客户端修复...")
    
    success = True
    
    # 测试OpenAI版本
    if not test_openai_version():
        success = False
    
    # 测试OpenAI客户端
    if not test_openai_client():
        success = False
    
    # 测试AgentService逻辑
    if not test_agent_service_import():
        success = False
    
    print("\n=== 测试结果 ===")
    if success:
        print("🎉 所有测试通过！OpenAI客户端修复成功！")
        print("\n修复内容:")
        print("- 升级OpenAI库从1.3.0到1.98.0")
        print("- 解决了proxies参数兼容性问题")
        print("- 客户端初始化现在使用最新的API")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    print("\n下一步:")
    print("1. 确保.env文件中的LLM配置正确")
    print("2. 测试实际的API调用功能")
    print("3. 验证Agent服务的完整功能")
