#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试流式响应修复
"""

import asyncio
import json
import sys
import os

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.schemas.agent import StreamChatResponse

async def test_stream_response_creation():
    """测试StreamChatResponse的创建和序列化"""
    print("=== 测试StreamChatResponse创建和序列化 ===")
    
    try:
        # 测试正常响应
        response = StreamChatResponse(
            conversation_id="test-123",
            content="这是测试内容",
            is_final=False
        )
        
        # 测试序列化
        data = response.model_dump()
        json_data = json.dumps(data, ensure_ascii=False)
        print(f"✅ 正常响应序列化成功: {json_data}")
        
        # 测试最终响应
        final_response = StreamChatResponse(
            conversation_id="test-123",
            content="",
            is_final=True
        )
        
        final_data = final_response.model_dump()
        final_json = json.dumps(final_data, ensure_ascii=False)
        print(f"✅ 最终响应序列化成功: {final_json}")
        
        # 测试SSE格式
        sse_format = f"data: {json_data}\n\n"
        print(f"✅ SSE格式正确: {repr(sse_format)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

async def test_exception_handling():
    """测试异常处理逻辑"""
    print("\n=== 测试异常处理逻辑 ===")
    
    try:
        # 模拟异常情况下的响应
        error_response = StreamChatResponse(
            conversation_id="test-error",
            content="生成回复时出错: 模拟错误",
            is_final=True
        )
        
        error_data = error_response.model_dump()
        error_json = json.dumps(error_data, ensure_ascii=False)
        print(f"✅ 错误响应序列化成功: {error_json}")
        
        return True
        
    except Exception as e:
        print(f"❌ 异常处理测试失败: {e}")
        return False

async def test_streaming_generator():
    """测试流式生成器逻辑"""
    print("\n=== 测试流式生成器逻辑 ===")
    
    try:
        async def mock_stream_generator():
            """模拟流式生成器"""
            try:
                # 模拟正常的流式响应
                chunks = ["你好", "，", "这是", "一个", "测试", "响应"]
                
                for chunk in chunks:
                    yield StreamChatResponse(
                        conversation_id="test-stream",
                        content=chunk,
                        is_final=False
                    )
                
                # 发送最终响应
                yield StreamChatResponse(
                    conversation_id="test-stream",
                    content="",
                    is_final=True
                )
                
            except Exception as e:
                # 异常处理
                yield StreamChatResponse(
                    conversation_id="test-stream",
                    content=f"生成回复时出错: {str(e)}",
                    is_final=True
                )
        
        # 测试生成器
        responses = []
        async for response in mock_stream_generator():
            responses.append(response)
            data = response.model_dump()
            json_data = json.dumps(data, ensure_ascii=False)
            print(f"📦 流式响应: {json_data}")
        
        print(f"✅ 流式生成器测试成功，共生成 {len(responses)} 个响应")
        return True
        
    except Exception as e:
        print(f"❌ 流式生成器测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试FastAPI流式响应修复")
    
    results = []
    
    # 运行所有测试
    results.append(await test_stream_response_creation())
    results.append(await test_exception_handling())
    results.append(await test_streaming_generator())
    
    # 汇总结果
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 测试结果汇总:")
    print(f"   通过: {passed}/{total}")
    print(f"   失败: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！流式响应修复验证成功！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
