#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试流式工具调用修复
"""

import asyncio
import json
import sys
import os
import httpx
import time

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def test_streaming_with_tools():
    """测试流式响应与工具调用"""
    print("=== 测试流式响应与工具调用 ===")
    
    # 测试数据 - 可能触发工具调用的查询
    test_data = {
        "message": "帮我查询一下当前时间，然后告诉我今天是星期几",
        "conversation_id": "test-tool-conversation-123",
        "stream": True
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print(f"发送请求到: http://localhost:8001/api/v1/agent/chat")
            print(f"请求数据: {json.dumps(test_data, ensure_ascii=False)}")
            
            async with client.stream(
                "POST",
                "http://localhost:8001/api/v1/agent/chat",
                json=test_data,
                headers={"Accept": "text/event-stream"}
            ) as response:
                print(f"响应状态码: {response.status_code}")
                
                if response.status_code != 200:
                    content = await response.aread()
                    print(f"错误响应: {content.decode()}")
                    return False
                
                # 读取流式响应
                chunk_count = 0
                full_content = []
                tool_calls_detected = False
                
                async for chunk in response.aiter_text():
                    if chunk.strip():
                        chunk_count += 1
                        print(f"接收到数据块 {chunk_count}: {chunk.strip()}")
                        
                        # 解析SSE数据
                        if chunk.startswith("data: "):
                            try:
                                data = json.loads(chunk[6:])  # 移除"data: "前缀
                                content = data.get("content", "")
                                if content:
                                    full_content.append(content)
                                
                                # 检查是否有工具调用信息
                                if data.get("tool_calls"):
                                    tool_calls_detected = True
                                    print(f"  检测到工具调用: {data['tool_calls']}")
                                
                                # 检查是否是最终响应
                                if data.get("is_final", False):
                                    print("  收到最终响应，测试完成")
                                    break
                            except json.JSONDecodeError as e:
                                print(f"  JSON解析失败: {e}")
                        
                        # 限制测试时间
                        if chunk_count > 50:
                            print("  达到最大块数限制，停止测试")
                            break
                
                full_response = "".join(full_content)
                print(f"\n完整响应内容: {full_response}")
                print(f"工具调用检测: {'是' if tool_calls_detected else '否'}")
                print(f"✅ 流式工具调用测试成功，共接收 {chunk_count} 个数据块")
                return True
                
    except httpx.ConnectError:
        print("❌ 连接失败：服务器未启动或端口不正确")
        return False
    except httpx.TimeoutException:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

async def test_streaming_without_tools():
    """测试流式响应无工具调用"""
    print("\n=== 测试流式响应无工具调用 ===")
    
    # 测试数据 - 不会触发工具调用的简单查询
    test_data = {
        "message": "请简单介绍一下Python编程语言",
        "conversation_id": "test-no-tool-conversation-456",
        "stream": True
    }
    
    try:
        async with httpx.AsyncClient(timeout=20.0) as client:
            print(f"发送请求到: http://localhost:8001/api/v1/agent/chat")
            print(f"请求数据: {json.dumps(test_data, ensure_ascii=False)}")
            
            async with client.stream(
                "POST",
                "http://localhost:8001/api/v1/agent/chat",
                json=test_data,
                headers={"Accept": "text/event-stream"}
            ) as response:
                print(f"响应状态码: {response.status_code}")
                
                if response.status_code != 200:
                    content = await response.aread()
                    print(f"错误响应: {content.decode()}")
                    return False
                
                # 读取流式响应
                chunk_count = 0
                full_content = []
                
                async for chunk in response.aiter_text():
                    if chunk.strip():
                        chunk_count += 1
                        
                        # 解析SSE数据
                        if chunk.startswith("data: "):
                            try:
                                data = json.loads(chunk[6:])
                                content = data.get("content", "")
                                if content:
                                    full_content.append(content)
                                
                                # 检查是否是最终响应
                                if data.get("is_final", False):
                                    print("  收到最终响应，测试完成")
                                    break
                            except json.JSONDecodeError as e:
                                print(f"  JSON解析失败: {e}")
                        
                        # 限制测试时间
                        if chunk_count > 30:
                            print("  达到最大块数限制，停止测试")
                            break
                
                full_response = "".join(full_content)
                print(f"完整响应内容: {full_response[:100]}...")
                print(f"✅ 无工具调用流式测试成功，共接收 {chunk_count} 个数据块")
                return True
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

async def test_non_streaming():
    """测试非流式响应"""
    print("\n=== 测试非流式响应 ===")
    
    test_data = {
        "message": "你好，请简单回复",
        "conversation_id": "test-non-stream-789",
        "stream": False
    }
    
    try:
        async with httpx.AsyncClient(timeout=15.0) as client:
            response = await client.post(
                "http://localhost:8001/api/v1/agent/chat",
                json=test_data,
                headers={"Accept": "text/event-stream"}
            )
            
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                # 非流式也会返回SSE格式
                content = response.text
                print(f"响应内容: {content[:200]}...")
                print("✅ 非流式响应测试成功")
                return True
            else:
                print(f"❌ 响应失败: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def check_server_status():
    """检查服务器状态"""
    print("=== 检查服务器状态 ===")
    
    import subprocess
    try:
        result = subprocess.run(
            ["lsof", "-i", ":8001"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            print("✅ 服务器正在端口8001运行")
            return True
        else:
            print("❌ 端口8001未被占用，服务器可能未启动")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("⚠️  无法检查服务器状态")
        return True

async def main():
    """主测试函数"""
    print("🚀 开始测试流式工具调用修复")
    print("=" * 60)
    
    # 检查服务器状态
    server_running = check_server_status()
    
    if not server_running:
        print("\n💡 启动服务器的命令:")
        print("   cd backend && python -m uvicorn app.main:app --host 0.0.0.0 --port 8001")
        print("\n⚠️  请先启动服务器，然后重新运行此测试")
        return False
    
    results = []
    
    print(f"\n⏰ 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行测试
    results.append(await test_non_streaming())
    results.append(await test_streaming_without_tools())
    results.append(await test_streaming_with_tools())
    
    # 汇总结果
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 测试结果汇总:")
    print(f"   通过: {passed}/{total}")
    print(f"   失败: {total - passed}/{total}")
    print(f"   完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    if passed == total:
        print("🎉 所有测试通过！流式工具调用修复验证成功！")
        print("\n✅ 修复效果:")
        print("   - 工具调用检测使用非流式调用，避免Stream对象错误")
        print("   - 最终答案生成支持流式响应")
        print("   - 'Stream' object has no attribute 'choices' 错误已解决")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
