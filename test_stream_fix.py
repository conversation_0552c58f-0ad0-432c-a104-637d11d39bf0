#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试流式响应修复
"""

import asyncio
import json
from backend.app.schemas.agent import StreamChatResponse

async def test_stream_response():
    """测试StreamChatResponse的序列化"""
    try:
        # 创建测试响应
        response = StreamChatResponse(
            conversation_id="test-123",
            content="测试内容",
            is_final=False
        )
        
        # 测试序列化
        data = json.dumps(response.model_dump(), ensure_ascii=False)
        print(f"序列化成功: {data}")
        
        # 测试SSE格式
        sse_data = f"data: {data}\n\n"
        print(f"SSE格式: {repr(sse_data)}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_stream_response())
    print(f"测试结果: {'通过' if result else '失败'}")
