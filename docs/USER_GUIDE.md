# 用户手册

## 系统概述

大模型Agent应用平台是一个集成了多种AI功能的智能应用平台，支持知识库管理、智能问答、数据分析、可视化等功能。

## 快速入门

### 1. 注册和登录

#### 注册新用户
1. 访问系统首页，点击"注册"按钮
2. 填写用户名和密码
3. 点击"注册"完成账户创建
4. 系统自动跳转到登录页面

#### 用户登录
1. 在登录页面输入用户名和密码
2. 点击"登录"按钮
3. 登录成功后自动跳转到主页面

### 2. 个人信息管理
- 访问"个人信息"页面查看账户信息
- 可以查看用户名、角色等基本信息
- 支持登出功能

## 知识库管理

### 1. 知识库列表
- 访问"知识库"页面查看所有知识库
- 支持按标题、创建时间等条件筛选
- 点击"详情"查看知识库详细信息

### 2. 创建知识库
1. 点击"新建知识库"按钮
2. 填写知识库标题
3. 选择输入方式：
   - **文本输入**：直接在文本框中输入内容
   - **文件上传**：点击"上传文件"选择文档文件
4. 点击"提交"开始处理
5. 系统自动进行分词、向量化处理

### 3. 知识库详情
- 查看知识库基本信息（标题、创建时间、内容）
- 查看分片信息（分片序号、内容、向量ID）
- 查看分片长度分布图表

### 4. RAG智能问答
1. 在知识库详情页面进行问答
2. 输入问题，系统基于向量检索和大模型生成答案
3. 查看生成的答案和相关分片信息

## 数据可视化

### 1. 配置数据源
1. 访问"数据可视化"页面
2. 输入数据库连接字符串（格式：mysql://user:password@host:port/db）
3. 输入SQL查询语句
4. 选择图表类型（柱状图、折线图、饼图等）

### 2. 查看可视化结果
- 系统执行SQL查询并返回数据
- 自动生成对应的图表
- 支持图表交互和导出

## ChatBI智能分析

### 1. 开始对话
1. 访问"ChatBI"页面
2. 配置数据库连接字符串
3. 在对话框中输入分析需求

### 2. 多轮对话
- 支持多轮对话，系统会记住上下文
- 可以基于之前的分析结果进行深入分析
- 系统自动生成SQL并执行

### 3. 结果展示
- 查看生成的SQL语句
- 查看查询结果表格
- 查看可视化图表

## Text2SQL查询

### 1. 配置数据库
1. 访问"Text2SQL"页面
2. 输入数据库连接字符串
3. 系统自动获取数据库结构信息

### 2. 自然语言查询
1. 在文本框中输入自然语言问题
2. 系统自动生成对应的SQL语句
3. 执行SQL并返回结果

### 3. 结果查看
- 查看生成的SQL语句
- 查看查询结果表格
- 支持结果导出

## MCP工具调用

### 1. 选择MCP Server
1. 访问"MCP工具"页面
2. 从下拉列表中选择MCP Server
3. 系统自动加载该Server的工具列表

### 2. 查看工具信息
- 查看工具名称和描述
- 查看工具参数Schema
- 了解工具功能和使用方法

### 3. 调用工具
1. 选择要调用的工具
2. 根据参数Schema填写参数（JSON格式）
3. 点击"调用工具"执行
4. 查看调用结果

## 界面操作指南

### 1. 导航菜单
- 顶部导航栏包含主要功能模块
- 支持快速切换不同功能页面
- 显示当前用户信息

### 2. 表格操作
- 支持排序和筛选
- 支持分页显示
- 支持数据导出

### 3. 表单操作
- 必填字段会有红色星号标识
- 支持表单验证和错误提示
- 支持表单重置功能

### 4. 文件上传
- 支持拖拽上传
- 支持文件类型限制
- 显示上传进度

### 5. 图表交互
- 支持图表缩放和平移
- 支持数据点悬停显示详情
- 支持图表导出

## 常见问题

### 1. 登录问题
**Q: 忘记密码怎么办？**
A: 目前系统不支持密码重置功能，请联系管理员重置密码。

**Q: 登录失败怎么办？**
A: 检查用户名和密码是否正确，确保账户未被锁定。

### 2. 知识库问题
**Q: 上传文件失败怎么办？**
A: 检查文件格式是否支持，文件大小是否超限，网络连接是否正常。

**Q: 知识库处理失败怎么办？**
A: 检查文件内容是否正常，系统资源是否充足，联系管理员查看日志。

### 3. 数据库连接问题
**Q: 数据库连接失败怎么办？**
A: 检查数据库连接字符串格式是否正确，数据库服务是否正常，网络连接是否正常。

**Q: SQL执行失败怎么办？**
A: 检查SQL语法是否正确，数据库权限是否足够，数据是否存在。

### 4. MCP工具问题
**Q: MCP Server连接失败怎么办？**
A: 检查Server路径是否正确，Server服务是否正常，网络连接是否正常。

**Q: 工具调用失败怎么办？**
A: 检查参数格式是否正确，参数值是否有效，工具是否可用。

## 最佳实践

### 1. 知识库管理
- 定期清理无用的知识库
- 使用有意义的标题和描述
- 合理分割文档内容

### 2. 数据分析
- 使用清晰的SQL查询语句
- 合理选择图表类型
- 注意数据安全和隐私

### 3. 工具使用
- 仔细阅读工具说明
- 正确填写参数
- 及时查看调用结果

### 4. 系统使用
- 定期备份重要数据
- 及时更新系统版本
- 关注系统公告和更新

## 联系支持

如遇到问题或需要帮助，请：
1. 查看常见问题解答
2. 查看系统日志
3. 联系技术支持团队
4. 提交问题反馈 