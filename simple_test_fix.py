#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单测试流式响应修复
"""

import asyncio
import json
import httpx

async def simple_test():
    """简单测试"""
    print("🧪 简单测试流式响应修复")
    
    test_data = {
        "message": "你好，请简单回复一下",
        "conversation_id": "simple-test",
        "stream": True
    }
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            print("发送请求...")
            
            async with client.stream(
                "POST",
                "http://localhost:8001/api/v1/agent/chat",
                json=test_data,
                headers={"Accept": "text/event-stream"}
            ) as response:
                print(f"状态码: {response.status_code}")
                
                if response.status_code != 200:
                    content = await response.aread()
                    print(f"错误: {content.decode()}")
                    return False
                
                chunk_count = 0
                async for chunk in response.aiter_text():
                    if chunk.strip():
                        chunk_count += 1
                        print(f"块 {chunk_count}: {chunk.strip()}")
                        
                        if chunk.startswith("data: "):
                            try:
                                data = json.loads(chunk[6:])
                                if data.get("is_final", False):
                                    print("✅ 收到最终响应")
                                    break
                            except:
                                pass
                        
                        if chunk_count > 10:  # 限制输出
                            print("✅ 测试成功，停止")
                            break
                
                return True
                
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(simple_test())
    print(f"结果: {'成功' if result else '失败'}")
