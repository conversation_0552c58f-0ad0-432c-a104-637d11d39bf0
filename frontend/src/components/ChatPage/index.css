.chat-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #ffffff;
}

.chat-layout {
  display: flex;
  height: 100%;
  width: 100%;
}

.chat-history {
  width: 280px;
  min-width: 200px;
  max-width: 340px;
  background: #f7f8fa;
  border-right: 1px solid #e5e6eb;
  overflow-y: auto;
  height: 100%;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 24px 24px 0 24px;
}

.chat-input-area {
  display: flex;
  padding: 16px;
  background: #ffffff;
  flex-shrink: 0;
}

.chat-input-container {
  position: relative;
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: flex-end;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  padding: 8px;
  transition: all 0.3s;
}

.chat-input-container:focus-within {
  border-color: #1890ff;
  box-shadow: none;
}

.ant-input-outlined:focus-within {
  /* 不要box-shadow */
  box-shadow: none;
}

.chat-input {
  flex: 1;
  border: none;
  outline: none;
  resize: none;
  min-height: 30px;
  max-height: 50px;
  line-height: 1.5;
  font-size: 14px;
  background: transparent;
  font-family: inherit;
}

.chat-input-buttons {
  position: absolute;
  right: 16px;
  bottom: 16px;
  z-index: 2;
  display: flex;
  align-items: flex-end;
  gap: 4px;
}

.chat-input-container textarea.chat-input {
  flex: 1;
  min-width: 0;
  overflow-y: auto;
  width: 100%;
  box-sizing: border-box;
  resize: none;
  min-height: 30px;
  max-height: 50px;
  padding-right: 0px; /* 预留按钮宽度 */
}

.user-info {
  padding: 12px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-info:hover {
  background: #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-sidebar {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s;
  }

  .chat-sidebar.open {
    transform: translateX(0);
  }

  .chat-main {
    width: 100%;
  }

  .chat-messages {
    padding: 12px;
  }

  .chat-input-area {
    padding: 12px;
  }
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  font-size: 16px;
}

.empty-state-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #d9d9d9;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

/* 工具提示样式 */
.tool-tips {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tool-tip {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
}

.chat-content {
  padding: 0;
  height: 100%;
}

.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.chat-history-section {
  flex: 1;
  overflow: hidden;
  padding: 20px;
  padding-bottom: 120px; /* 为输入框和用户信息留出空间 */
}

.chat-input-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e8e8e8;
  padding: 20px;
  z-index: 1000;
}

/* 左下角用户信息固定定位 */
.user-info-fixed {
  position: fixed;
  bottom: 20px;
  left: 20px;
  z-index: 1001;
  pointer-events: auto;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .chat-history-section {
    padding: 15px;
    padding-bottom: 140px; /* 移动端增加更多底部空间 */
  }
  
  .chat-input-section {
    padding: 15px;
  }
  
  .user-info-fixed {
    bottom: 15px;
    left: 15px;
  }
}

@media (max-width: 480px) {
  .chat-history-section {
    padding: 10px;
    padding-bottom: 150px;
  }
  
  .chat-input-section {
    padding: 10px;
  }
  
  .user-info-fixed {
    bottom: 10px;
    left: 10px;
  }
} 